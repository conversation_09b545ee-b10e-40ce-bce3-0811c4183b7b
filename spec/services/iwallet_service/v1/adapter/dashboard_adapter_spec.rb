require 'rails_helper'

RSpec.describe IwalletService::V1::Adapter::DashboardAdapter do
  let(:mock_client) { instance_double(IwalletService::V1::Utils::Client) }
  let(:adapter) { described_class.new }

  before do
    allow_any_instance_of(described_class).to receive(:client).and_return(mock_client)
  end

  # not a real endpoint, just a sample
  describe '#users_list' do
    let(:expected_response) { { data: [ { id: 1, name: '<PERSON>' } ] } }

    context 'when called without parameters' do
      it 'makes GET request to dashboard/users endpoint' do
        allow(mock_client).to receive(:get).and_return(expected_response)

        result = adapter.users_list

        expect(mock_client).to have_received(:get).with('dashboard/users', {})
        expect(result).to eq(expected_response)
      end
    end

    context 'when called with parameters' do
      let(:params) { { limit: 10, page: 1 } }

      it 'passes parameters to client' do
        allow(mock_client).to receive(:get).and_return(expected_response)

        result = adapter.users_list(params)

        expect(mock_client).to have_received(:get).with('dashboard/users', params)
        expect(result).to eq(expected_response)
      end
    end
  end

  describe '#employee_count' do
    let(:expected_response) { { count: 5 } }

    context 'when called without parameters' do
      it 'makes GET request to customer/dashboard/employee_count endpoint' do
        allow(mock_client).to receive(:get).and_return(expected_response)

        result = adapter.employee_count

        expect(mock_client).to have_received(:get).with('customer/dashboard/employee_count', {})
        expect(result).to eq(expected_response)
      end
    end

    context 'when called with parameters' do
      let(:params) { { active: true } }

      it 'passes parameters to client' do
        allow(mock_client).to receive(:get).and_return(expected_response)

        result = adapter.employee_count(params)

        expect(mock_client).to have_received(:get).with('customer/dashboard/employee_count', params)
        expect(result).to eq(expected_response)
      end
    end
  end
end
