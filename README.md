# iWallet Co Panel
## Requirements

- Ruby 3.2.2
- Rails 8.0.2.1
- PostgreSQL
- Redis (for Sidekiq)
- Git/Git-Flow & Semantic Versioning

---

## Setup

1. Clone the repository:
```bash
✗ cd existing_repo
✗ <NAME_EMAIL>:iwallet/iwalletCoPanel.git
✗ git flow init
```

2. Add credentials keys:
```bash
* Get the master key and create config/master.key
```

3. Install gems:
```bash
✗ bundle install
```

4. Setup database:
```bash
✗ rails db:create db:migrate
```

5. Start Rails server:
```bash
✗ ./bin/dev
```

## Linting
```bash
✗ bundle exec rubocop
```

## Standard Testing
* Running tests normally
```bash
✗ bundle exec rspec
```

## Parallel testing
* Parallel test setup
```bash
✗ RAILS_ENV=test bundle exec rake parallel:drop parallel:create parallel:prepare parallel:migrate
```

* Running tests in parallel
```bash
✗ bundle exec parallel_rspec spec/ -n 4
```

