require 'rails_helper'

RSpec.describe Iwallet::V1::DataProcessor::Base do
  describe '#initialize' do
    it 'sets params and error correctly' do
      processor = described_class.new({ user_id: 1 })
      expect(processor.params).to eq({ user_id: 1 })
      expect(processor.error).to be_nil
      expect(processor.data).to be_nil
    end

    it 'defaults to empty params' do
      processor = described_class.new
      expect(processor.params).to eq({})
    end
  end

  describe '#call' do
    it 'raises NotImplementedError' do
      processor = described_class.new
      expect { processor.call }.to raise_error(NotImplementedError, 'Subclasses must implement #call')
    end
  end

  describe '#success?' do
    it 'returns true when error is nil' do
      processor = described_class.new
      expect(processor.success?).to be true
    end

    it 'returns false when error is present' do
      processor = described_class.new
      processor.error = 'Something went wrong'
      expect(processor.success?).to be false
    end
  end
end
