class Iwallet::V1::DataProcessor::Dashboard::EmployeeCount < Iwallet::V1::DataProcessor::Base
  def call
    service = IwalletService::V1::Adapter::DashboardAdapter.new(id: id)
    result  = service.employee_count(params)

    @data = handle_data(result) and true
  rescue => e
    @error = e.message and false
  end

  private

  def handle_data(result)
    return {} unless result.data

    {
      employee_count: result.data.employee_count
    }
  end
end
