require 'rails_helper'

RSpec.describe IwalletService::V1::Adapter::BaseAdapter do
  let(:mock_credentials) do
    double('credentials',
      iwallet: double('iwallet',
        auth_token: 'test_token_123',
        base_url: 'https://example.com/api/co_panel/v1'
      )
    )
  end

  before do
    allow(Rails.application).to receive(:credentials).and_return(mock_credentials)
  end

  describe '#initialize' do
    it 'creates options with credentials' do
      expect(IwalletService::V1::Options).to receive(:new).with(
        auth_token: 'test_token_123',
        base_url: 'https://example.com/api/co_panel/v1'
      ).and_call_original

      described_class.new
    end

    it 'creates client with options' do
      options_instance = instance_double(IwalletService::V1::Options)
      allow(IwalletService::V1::Options).to receive(:new).and_return(options_instance)

      expect(IwalletService::V1::Utils::Client).to receive(:new).with(options: options_instance)

      described_class.new
    end

    it 'sets client as accessible attribute' do
      client_instance = instance_double(IwalletService::V1::Utils::Client)
      allow(IwalletService::V1::Utils::Client).to receive(:new).and_return(client_instance)

      adapter = described_class.new

      expect(adapter.client).to eq(client_instance)
    end
  end
end
