Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root "co_panel/v1/customers/dashboard#index"

  namespace :co_panel do
    namespace :v1, path: '' do
      namespace :customers, path: 'customers/:customer_id' do
        draw :dashboard
      end
    end
  end

  draw :sidekiq

  # Custom match and redirect for not valid routes
  match '', to: 'application#page_not_found', via: :all if Rails.env.production?
  match '*unmatched', to: 'application#page_not_found', via: :all if Rails.env.production?
end
