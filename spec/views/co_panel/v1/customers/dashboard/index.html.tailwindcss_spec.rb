require 'rails_helper'

RSpec.describe 'co_panel/dashboard/index.html.erb', type: :view do
  before do
    # Mock the route helper
    allow(view).to receive(:co_panel_dashboard_users_list_path).and_return('/co_panel/dashboard/users_list')
    allow(view).to receive(:co_panel_dashboard_employee_count_path).and_return('/co_panel/dashboard/employee_count')
  end

  it 'renders turbo_frame_tag with correct attributes' do
    render

    # not a real endpoint, just a sample
    expect(rendered).to have_selector('turbo-frame[id="users_list"]')
    expect(rendered).to have_selector('turbo-frame[src="/co_panel/dashboard/users_list"]')

    expect(rendered).to have_selector('turbo-frame[id="employee_count"]')
    expect(rendered).to have_selector('turbo-frame[src="/co_panel/dashboard/employee_count"]')
  end
end
