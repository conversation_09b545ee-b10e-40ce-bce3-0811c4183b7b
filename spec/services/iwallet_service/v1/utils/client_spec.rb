require 'rails_helper'

RSpec.describe IwalletService::V1::Utils::Client do
  let(:auth_token) { 'test_token' }
  let(:base_url) { 'https://example.com/api/co_panel/v1' }
  let(:options) { double('options', auth_token: auth_token, base_url: base_url) }
  let(:client) { described_class.new(options: options) }

  let(:mock_http) { instance_double(Net::HTTP) }
  let(:mock_response) { instance_double(Net::HTTPResponse) }

  before do
    allow(Net::HTTP).to receive(:new).and_return(mock_http)
    allow(mock_http).to receive(:use_ssl=)
    allow(mock_http).to receive(:open_timeout=)
    allow(mock_http).to receive(:read_timeout=)
    allow(mock_http).to receive(:request).and_return(mock_response)
  end

  describe '#get' do
    context 'when response contains array data' do
      it 'makes GET request and parses array data' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": [{"id": 1, "name": "<PERSON>"}, {"id": 2, "name": "<PERSON>"}]}')

        result = client.get('users', { limit: 10 })

        expect(Net::HTTP).to have_received(:new).with('example.com', 443)
        expect(result.data).to be_an(Array)
        expect(result.data.length).to eq(2)

        expect(result.data.first.id).to eq(1)
        expect(result.data.first.name).to eq("John")

        expect(result.data.last.id).to eq(2)
        expect(result.data.last.name).to eq("Jane")
      end
    end

    context 'when response contains single object data' do
      it 'makes GET request and parses single object data' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": {"id": 1, "name": "John"}}')

        result = client.get('users')

        expect(Net::HTTP).to have_received(:new).with('example.com', 443)
        expect(result.data.id).to eq(1)
        expect(result.data.name).to eq("John")
      end
    end
  end

  describe '#post' do
    context 'when creating single record' do
      it 'makes POST request with body and returns single object' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": {"id": 1, "name": "John"}}')

        result = client.post('users', { name: 'John' })

        expect(Net::HTTP).to have_received(:new).with('example.com', 443)
        expect(result.data.id).to eq(1)
        expect(result.data.name).to eq("John")
      end
    end

    context 'when creating multiple records' do
      it 'makes POST request and returns array data' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": [{"id": 1, "name": "John"}, {"id": 2, "name": "Jane"}]}')

        result = client.post('users/bulk', [ { name: 'John' }, { name: 'Jane' } ])

        expect(Net::HTTP).to have_received(:new).with('example.com', 443)
        expect(result.data).to be_an(Array)
        expect(result.data.length).to eq(2)
        expect(result.data.first.name).to eq("John")
      end
    end
  end

  describe '#put' do
    it 'makes PUT request with body and returns updated object' do
      allow(mock_response).to receive(:content_type).and_return('application/json')
      allow(mock_response).to receive(:body).and_return('{"data": {"id": 1, "name": "Jane", "updated": true}}')

      result = client.put('users/1', { name: 'Jane' })

      expect(result.data.updated).to be true
      expect(result.data.name).to eq("Jane")
    end
  end

  describe '#delete' do
    context 'when deleting single record' do
      it 'makes DELETE request and returns confirmation' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": {"deleted": true, "id": 1}}')

        result = client.delete('users/1', { force: true })

        expect(result.data.deleted).to be true
        expect(result.data.id).to eq(1)
      end
    end

    context 'when deleting multiple records' do
      it 'makes DELETE request and returns array of deleted records' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": [{"deleted": true, "id": 1}, {"deleted": true, "id": 2}]}')

        result = client.delete('users', { ids: [ 1, 2 ] })

        expect(result.data).to be_an(Array)
        expect(result.data.first.deleted).to be true
      end
    end
  end

  describe 'response handling' do
    context 'when response is HTML' do
      it 'returns body as string' do
        allow(mock_response).to receive(:content_type).and_return('text/html; charset=utf-8')
        allow(mock_response).to receive(:body).and_return('<html>test</html>')

        result = client.get('test')

        expect(result).to eq('<html>test</html>')
      end
    end

    context 'when response is JSON with nested data structure' do
      it 'parses complex JSON to accessible format' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": {"user": {"id": 1, "profile": {"name": "John"}}}}')

        result = client.get('test')

        expect(result.data.user.id).to eq(1)
        expect(result.data.user.profile.name).to eq("John")
      end
    end

    context 'when response contains empty data' do
      it 'handles empty array data' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": []}')

        result = client.get('test')

        expect(result.data).to eq([])
        expect(result.data).to be_empty
      end

      it 'handles empty object data' do
        allow(mock_response).to receive(:content_type).and_return('application/json')
        allow(mock_response).to receive(:body).and_return('{"data": {}}')

        result = client.get('test')

        expect(result.data).to be_an(OpenStruct)
        expect(result.data.to_h).to be_empty
      end
    end
  end

  describe 'headers' do
    it 'includes correct headers' do
      allow(mock_response).to receive(:content_type).and_return('application/json')
      allow(mock_response).to receive(:body).and_return('{}')

      expect(Net::HTTP::Get).to receive(:new).with(
        anything,
        hash_including(
          'Content-Type' => 'application/json',
          'Accept' => 'application/json',
          'Authorization' => 'Bearer test_token',
          'Version' => 'v1'
        )
      )

      client.get('test')
    end
  end
end
