require 'net/http'
require 'ostruct'

class IwalletService::V1::Utils::Client
  def initialize(options:)
    @options = options
  end

  def get(endpoint, params = {})
    send_request(endpoint, :get, params: params)
  end

  def post(endpoint, body = {})
    send_request(endpoint, :post, body: body)
  end

  def put(endpoint, body = {})
    send_request(endpoint, :put, body: body)
  end

  def delete(endpoint, params = {})
    send_request(endpoint, :delete, params: params)
  end

  private

  attr_reader :options

  def send_request(endpoint, method, params: {}, body: {})
    full_path = "#{options.base_url}/#{endpoint}"

    uri = URI.parse(full_path)
    uri.query = URI.encode_www_form(params) if params.any?

    # Prepare request headers
    headers = add_headers

    request = \
    case method
    when :get
      Net::HTTP::Get.new(uri, headers)
    when :post
      Net::HTTP::Post.new(uri, headers)
    when :put
      Net::HTTP::Put.new(uri, headers)
    when :delete
      Net::HTTP::Delete.new(uri, headers)
    else
      raise "Unsupported HTTP method: #{method}"
    end

    request.body = body.to_json if body.any?

    https = Net::HTTP.new(uri.host, uri.port)
    https.use_ssl = Rails.env.production?
    https.open_timeout = 10
    https.read_timeout = 150

    response = https.request(request)
    response =  \
    case response.content_type
    when 'text/html; charset=utf-8'
      response.body
    else
      JSON.parse(response.body, object_class: OpenStruct)
    end

    response
  end

  def add_headers
    {
      'Content-Type' => 'application/json',
      'Accept' => 'application/json',
      'Authorization' => "Bearer #{options.auth_token}",
      'Version' => 'v1'
    }
  end
end
