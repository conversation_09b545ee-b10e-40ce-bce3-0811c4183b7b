class CoPanel::V1::Customers::DashboardController < CoPanel::V1::Customers::ApplicationController
  def index
  end

  # not a real endpoint, just a sample
  def users_list
    processor = Iwallet::V1::DataProcessor::Dashboard::UsersList.new(id: @customer_id)
    processor.call

    @data  = processor.data
    @error = processor.error

    respond_to do |format|
      if processor.success?
        format.html { render :users_list, status: :ok }
        format.json { render json: { data: @data }, status: :ok }
      else
        format.html { render :users_list, status: :unprocessable_entity }
        format.json { render json: { error: @error }, status: :unprocessable_entity }
      end
    end
  end

  def employee_count
    processor = Iwallet::V1::DataProcessor::Dashboard::EmployeeCount.new(id: @customer_id)
    processor.call

    @data  = processor.data
    @error = processor.error

    respond_to do |format|
      if processor.success?
        format.html { render :employee_count, status: :ok }
        format.json { render json: { data: @data }, status: :ok }
      else
        format.html { render :employee_count, status: :unprocessable_entity }
        format.json { render json: { error: @error }, status: :unprocessable_entity }
      end
    end
  end
end
