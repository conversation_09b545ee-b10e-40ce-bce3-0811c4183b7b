# not a real endpoint, just a sample
class Iwallet::V1::DataProcessor::Dashboard::UsersList < Iwallet::V1::DataProcessor::Base
  def call
    service = IwalletService::V1::Adapter::DashboardAdapter.new(id: id)
    result  = service.users_list(params)

    @data = handle_data(result) and true
  rescue => e
    @error = e.message and false
  end

  private

  # Çoklu veri döndüren versiyon(array):
  # {
  #   "data": [
  #     { "id": 1, "name": "<PERSON><PERSON>" },
  #     { "id": 2, "name": "Ahmet" }
  #   ]
  # }
  def handle_data(result)
    return [] unless result.data

    result.data.map do |item|
      {
        id: item.id
      }
    end
  end

  # Tekil veri döndüren versiyon (hash):
  # {
  #   "data": {
  #     "id": 1,
  #     "name": 'Fat<PERSON>'
  #   }
  # }
  # def handle_data(result)
  #   return {} unless result.data

  #   {
  #     id: result.data.id
  #   }
  # end
end
