# not a real endpoint, just a sample
require 'rails_helper'
require 'ostruct'

RSpec.describe Iwallet::V1::DataProcessor::Dashboard::UsersList do
  let(:params) { { limit: 10 } }
  let(:processor) { described_class.new(params) }
  let(:adapter) { instance_double(IwalletService::V1::Adapter::DashboardAdapter) }

  before do
    allow(IwalletService::V1::Adapter::DashboardAdapter).to receive(:new).and_return(adapter)
  end

  describe '#call' do
    let(:mock_user1) { OpenStruct.new(id: 1) }
    let(:mock_user2) { OpenStruct.new(id: 2) }
    let(:result) { OpenStruct.new(data: [ mock_user1, mock_user2 ]) }

    context 'when service returns data' do
      before do
        allow(adapter).to receive(:users_list).with(params).and_return(result)
      end

      it 'returns true and sets formatted user data' do
        expect(processor.call).to be true
        expect(processor.data).to eq([ { id: 1 }, { id: 2 } ])
      end

      it 'remains successful' do
        processor.call
        expect(processor.success?).to be true
      end

      it 'has no error' do
        processor.call
        expect(processor.error).to be_nil
      end
    end

    context 'when service returns no data' do
      before do
        allow(adapter).to receive(:users_list).with(params).and_return(OpenStruct.new(data: nil))
      end

      it 'returns true and sets empty array' do
        expect(processor.call).to be true
        expect(processor.data).to eq([])
      end

      it 'remains successful' do
        processor.call
        expect(processor.success?).to be true
      end
    end

    context 'when service raises error' do
      before do
        allow(adapter).to receive(:users_list).and_raise(StandardError.new('Service error'))
      end

      it 'returns false and sets error message' do
        expect(processor.call).to be false
        expect(processor.error).to eq('Service error')
      end

      it 'is not successful' do
        processor.call
        expect(processor.success?).to be false
      end

      it 'data is nil when error occurs' do
        processor.call
        expect(processor.data).to be_nil
      end
    end
  end
end
