require 'rails_helper'
require 'ostruct'

RSpec.describe Iwallet::V1::DataProcessor::Dashboard::EmployeeCount do
  let(:params) { { active: true } }
  let(:processor) { described_class.new(params) }
  let(:adapter) { instance_double(IwalletService::V1::Adapter::DashboardAdapter) }

  before do
    allow(IwalletService::V1::Adapter::DashboardAdapter).to receive(:new).and_return(adapter)
  end

  describe '#call' do
    let(:result) { OpenStruct.new(data: OpenStruct.new(employee_count: 5)) }

    context 'when service returns data' do
      before do
        allow(adapter).to receive(:employee_count).with(params).and_return(result)
      end

      it 'returns true and sets formatted employee count data' do
        expect(processor.call).to be true
        expect(processor.data).to eq({ employee_count: 5 })
      end

      it 'remains successful' do
        processor.call
        expect(processor.success?).to be true
      end

      it 'has no error' do
        processor.call
        expect(processor.error).to be_nil
      end
    end

    context 'when service returns no data' do
      before do
        allow(adapter).to receive(:employee_count).with(params).and_return(OpenStruct.new(data: nil))
      end

      it 'returns true and sets empty hash' do
        expect(processor.call).to be true
        expect(processor.data).to eq({})
      end

      it 'remains successful' do
        processor.call
        expect(processor.success?).to be true
      end
    end

    context 'when service raises error' do
      before do
        allow(adapter).to receive(:employee_count).and_raise(StandardError.new('Service error'))
      end

      it 'returns false and sets error message' do
        expect(processor.call).to be false
        expect(processor.error).to eq('Service error')
      end

      it 'is not successful' do
        processor.call
        expect(processor.success?).to be false
      end

      it 'data is nil when error occurs' do
        processor.call
        expect(processor.data).to be_nil
      end
    end
  end
end
