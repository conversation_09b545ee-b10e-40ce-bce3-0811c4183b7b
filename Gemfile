source 'https://rubygems.org'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails', '~> 8.0.2', '>= *******'
# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem 'propshaft'
# Use postgresql as the database for Active Record
gem 'pg', '~> 1.1'
# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '>= 5.0'
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem 'importmap-rails'
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem 'turbo-rails'
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem 'stimulus-rails'
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem 'tailwindcss-rails'
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem 'jbuilder'

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem 'bcrypt', '~> 3.1.7'

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[ windows jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem 'solid_cache'
gem 'solid_queue'
gem 'solid_cable'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
# gem 'kamal', require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem 'thruster', require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem 'image_processing', '~> 1.2'

# Nice Partials adds ad-hoc named content areas, or sections,
# to Action View partials with a lot of extra power on top.
# [https://github.com/bullet-train-co/nice_partials]
gem 'nice_partials', '~> 0.10.0'

# Tame Rails' multi-line logging into a single line per request.
# [https://github.com/roidrage/lograge]
gem 'lograge', '~> 0.14.0'

# Simple, efficient background processing for Ruby. [https://sidekiq.org/]
gem 'sidekiq', '~> 7.1', '>= 7.1.2'

# Use Redis adapter to run Action Cable in production
gem 'redis', '~> 5.4', '>= 5.4.1'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'debug', platforms: %i[ mri windows ], require: 'debug/prelude'

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem 'brakeman', require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem 'rubocop-rails-omakase', require: false

  # help to kill N+1 queries and unused eager loading. [https://github.com/flyerhzm/bullet]
  gem 'bullet', '~> 8.0.0'

  # rspec-rails is a testing framework for Rails 5+. [https://github.com/rspec/rspec-rails]
  gem 'rspec-rails', '~> 8.0.0'

  # ParallelTests splits tests into balanced groups (by number of lines or runtime) and
  # runs each group in a process with its own database. [https://github.com/grosser/parallel_tests]
  gem 'parallel_tests', '~> 2.7'

  # Easily generate fake data. [https://github.com/faker-ruby/faker]
  gem 'faker', '~> 3.4', '>= 3.4.2'

  # factory_bot_rails provides integration between factory_bot and rails 5.0 or newer.
  # [https://github.com/thoughtbot/factory_bot_rails]
  gem 'factory_bot_rails', '~> 6.2'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem 'web-console'

  #  When mail is sent from your application, Letter Opener will open a preview in the browser instead of sending.
  #  [https://github.com/ryanb/letter_opener]
  gem 'letter_opener', '~> 1.10'
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem 'capybara'
  gem 'selenium-webdriver'
end
