# Omakase Ruby styling for Rails
inherit_gem: { rubocop-rails-omakase: rubocop.yml }

# Overwrite or add rules to create your own house style
#
#
AllCops:
  TargetRubyVersion: 3.2.2
  DisabledByDefault: true
  Exclude:
    - "bin/**/*"
    - "vendor/**/*"
    - "public/**/*"

#
Bundler:
  Enabled: true
Bundler/OrderedGems:
  Enabled: false

# Use `[a, [b, c]]` not `[ a, [ b, c ] ]`
# Layout/SpaceInsideArrayLiteralBrackets:
#   Enabled: false

#
Layout/EmptyLineAfterMagicComment:
  Enabled: true
Layout/TrailingEmptyLines:
  Enabled: true
Layout/TrailingWhitespace:
  Enabled: true

#
Lint/BinaryOperatorWithIdenticalOperands:
  Enabled: true
Lint/CircularArgumentReference:
  Enabled: true
Lint/Debugger:
  Enabled: true
Lint/DeprecatedClassMethods:
  Enabled: true
Lint/DuplicateMethods:
  Enabled: false
Lint/DuplicateHashKey:
  Enabled: true
Lint/EachWithObjectArgument:
  Enabled: true
Lint/ElseLayout:
  Enabled: true
Lint/EmptyEnsure:
  Enabled: true
Lint/EmptyInterpolation:
  Enabled: true
Lint/EnsureReturn:
  Enabled: true
Lint/FloatOutOfRange:
  Enabled: true
Lint/FormatParameterMismatch:
  Enabled: true
Lint/LiteralAsCondition:
  Enabled: true
Lint/LiteralInInterpolation:
  Enabled: true
Lint/Loop:
  Enabled: true
Lint/NextWithoutAccumulator:
  Enabled: true
Lint/RandOne:
  Enabled: true
Lint/RequireParentheses:
  Enabled: true
Lint/RescueException:
  Enabled: true
Lint/RedundantStringCoercion:
  Enabled: false
Lint/RedundantCopDisableDirective:
  Enabled: true
Lint/RedundantSplatExpansion:
  Enabled: false
Lint/UnreachableCode:
  Enabled: true
Lint/UselessSetterCall:
  Enabled: true
Lint/Void:
  Enabled: true

#
Naming/ClassAndModuleCamelCase:
  Enabled: true

#
Style/StringLiterals:
  Enabled: true
  EnforcedStyle: "single_quotes"
  Exclude:
    - "config/**/*.rb"
    - "config.ru"
    - "db/*.rb"
    - "test/**/*"
    - "spec/rails_helper.rb"
    - "spec/spec_helper.rb"
    - "app/mailers/application_mailer.rb"
    - Rakefile

#
Style/FrozenStringLiteralComment:
  Enabled: false

#
Layout/LineLength:
  Enabled: true
  Max: 120
  IgnoreCopDirectives: true
  Exclude:
    - "spec/**/*"
    - "db/**/*"
    - "config/**/*"
