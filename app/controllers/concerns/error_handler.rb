module <PERSON>rror<PERSON>andler
  extend ActiveSupport::Concern

  included do
    if Rails.env.production?
      rescue_from Exception, with: :server_error
      rescue_from ActiveRecord::RecordNotFound, with: :record_not_found
      rescue_from ActionController::RoutingError, with: :page_not_found
    end
  end

  def server_error(exception)
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/500.html", layout: false, status: 500 }
      format.any  { head :not_found }
    end
  end

  def record_not_found(exception)
    respond_to do |format|
      format.html {
        render file: "#{Rails.root}/public/404.html", layout: false, status: :not_found }
      format.any  { head :not_found }
    end
  end

  def page_not_found
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404.html", layout: false, status: 404 }
      format.any  { head :not_found }
    end
  end
end
