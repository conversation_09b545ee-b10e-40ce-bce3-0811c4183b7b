require 'rails_helper'

RSpec.describe IwalletService::V1::Options do
  let(:auth_token) { 'test_token_123' }
  let(:base_url) { 'https://example.com/api/co_panel/v1' }

  describe '#initialize' do
    it 'sets auth_token and base_url' do
      options = described_class.new(auth_token: auth_token, base_url: base_url)

      expect(options.auth_token).to eq(auth_token)
      expect(options.base_url).to eq(base_url)
    end
  end

  describe 'attr_accessor' do
    subject { described_class.new(auth_token: auth_token, base_url: base_url) }

    it 'allows reading and writing auth_token' do
      new_token = 'new_token_456'
      subject.auth_token = new_token
      expect(subject.auth_token).to eq(new_token)
    end

    it 'allows reading and writing base_url' do
      new_url = 'https://new-api.iwallet.com'
      subject.base_url = new_url
      expect(subject.base_url).to eq(new_url)
    end
  end
end
