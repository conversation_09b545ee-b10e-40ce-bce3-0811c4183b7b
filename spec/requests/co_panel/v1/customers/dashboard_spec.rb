require 'rails_helper'

RSpec.describe CoPanel::V1::Customers::DashboardController, type: :controller do
  describe 'GET #index' do
    it 'renders the index template' do
      get :index

      expect(response).to have_http_status(:ok)
    end
  end

  describe 'GET #users_list' do
    let(:processor) { instance_double(Iwallet::V1::DataProcessor::Dashboard::UsersList) }
    let(:mock_data) { { data: [ { id: 1, name: '<PERSON>' } ] } }

    before do
      allow(Iwallet::V1::DataProcessor::Dashboard::UsersList).to receive(:new).and_return(processor)
      allow(processor).to receive(:call)
      allow(processor).to receive(:data).and_return(mock_data)
    end

    context 'when processor is successful' do
      before do
        allow(processor).to receive(:success?).and_return(true)
        allow(processor).to receive(:error).and_return(nil)
      end

      context 'with HTML format' do
        it 'renders the users_list template' do
          get :users_list

          expect(response).to have_http_status(:ok)
          expect(controller.instance_variable_get(:@data)).to eq(mock_data)
          expect(controller.instance_variable_get(:@error)).to be_nil
        end
      end

      context 'with JSON format' do
        it 'renders the users_list template' do
          get :users_list, format: :json
          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body, symbolize_names: true)).to eq({ data: mock_data })
        end
      end
    end

    context 'when processor fails' do
      let(:error_message) { 'Something went wrong' }

      before do
        allow(processor).to receive(:success?).and_return(false)
        allow(processor).to receive(:error).and_return(error_message)
      end

      context 'with HTML format' do
        it 'renders HTML with unprocessable_entity status' do
          get :users_list

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context 'with JSON format' do
        it 'returns JSON error with unprocessable_entity status' do
          get :users_list, format: :json

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body, symbolize_names: true)).to eq({ error: error_message })
        end
      end
    end

    it 'creates processor instance and calls it' do
      allow(processor).to receive(:success?).and_return(true)
      allow(processor).to receive(:error).and_return(nil)

      get :users_list

      expect(Iwallet::V1::DataProcessor::Dashboard::UsersList).to have_received(:new)
      expect(processor).to have_received(:call)
      expect(processor).to have_received(:data)
    end
  end
end
